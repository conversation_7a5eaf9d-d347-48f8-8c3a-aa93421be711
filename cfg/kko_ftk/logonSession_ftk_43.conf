
#   listen 할 주소및 포트 : logon 정보와 일치하는지 체크시 사용
#gw.ip = **************
gw.ip = ***************
gw.port = 43000

# 해당 (micro sec) 초만큼 휴식후 새연결을 대기한다.
process.sleeptime = 100000

# logon 정보를 요청하는 domain socket 정보
domain.logondb = /user/neoftk/domain/DB_logon_3
#domain.logondb = /home/<USER>/domain/DB_logon_3

# 생성 시킬 프로세스(sender/report) 가 사용할 기본 domain socket  정보
domain.path = /user/neoftk/domain
#domain.path = /home/<USER>/domain

# 생성 시킬 프로세스 (sender/report) 실행파일 위치
process.bindir = /home/<USER>/CLionProjects/ftalk_up/daemon_logon_ftk/bin
#process.bindir = /user/neoftk/daemon_logon_ftk/bin

# 생성 시킬 프로세스 (sender/report) 가 사용할 conf file path
#( 프로그램 생성시 4번째 인자로 프로세스 이름.conf 로 사용된다. )
process.cfgdir = /home/<USER>/CLionProjects/ftalk_up/cfg/kko_ftk
#process.cfgdir = /user/neoftk/cfg/kko_ftk

#생성 시킬 프로세스(sender/report) 가 사용할 로그 디렉토리
log.path = /data/log_ftk

# L4스위치 체크 제거
#gw.l4IP = ***************|***************
gw.l4IP = **************|**************
#gw.l4IP = ***********

