domain.logondb = /user/neoftk/domain/DB_logon_3
# 정보 갱신시 필요한 logonDB domain info

domain.monitor = /user/neoftk/domain/MO_monitor_3
# monitor 서버로 정보 전송을 위한 monitor domain info

domain.self = /user/neoftk/domain
# 어드민과 통신을 위한 자신의 domain info 패스

socket.linktimeout = 30
# 데이터또는 상태 전문 timeout

db.requesttimeout = 7
# senderDB 요청시 ack 실패 줄때까지 재시도 하는 sec
# 모듈에서 ack 실패 timeout 보다 길어서는 안된다. / 프로세스 처리시간까지 고려 한다면 최소 2초이상 작아야 된다.
# 현재 윈도우 모듈(logon5) 의경우 socket timeout 옵션에 의해서 timeout 처리 되며, timeout 시간은 15초이다.

path.mmscontent = /data/neoftk/CNT

db.uid = neoftk/neoftk
db.dsn = NEO226

db.mmsidhead = 2
# MMS_ID생성용 1호기는 1, 2호기는 2
